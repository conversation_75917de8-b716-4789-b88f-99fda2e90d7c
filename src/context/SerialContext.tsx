import React, { createContext, useContext, useState, useEffect, useRef, ReactNode } from 'react';
import { toast } from '@/components/ui/use-toast';

// Type definitions for our serial data
export type ForceData = {
  fx: number;
  fy: number;
  fz: number;
  cx: number;
  cy: number;
  rawData: number[];
  timestamp: number;
  sampleTimestamp: number; // microseconds, from device
  sampleId: number;        // sample counter, from device
};

export type ForcePlate = {
  id: string;
  port: SerialPort | null;
  reader: ReadableStreamDefaultReader<Uint8Array> | null;
  name: string;
  isConnected: boolean;
  isReading: boolean;
  data: ForceData[];
  lastData: ForceData | null;
};

export type DataFormat = 'json' | 'csv';

type SerialContextType = {
  availablePorts: SerialPort[];
  forcePlates: ForcePlate[];
  isScanning: boolean;
  scanForPorts: () => Promise<void>;
  connectToPort: (portInfo: SerialPort) => Promise<ForcePlate | null>;
  disconnectPort: (plateId: string) => Promise<void>;
  startReading: (plateId: string) => Promise<void>;
  stopReading: (plateId: string) => Promise<void>;
  zeroPlate: (plateId: string) => Promise<void>;
  saveData: (plateId: string, format?: DataFormat, fileName?: string) => Promise<void>;
  clearData: (plateId: string) => void;
};

const SerialContext = createContext<SerialContextType | undefined>(undefined);

type SerialProviderProps = {
  children: ReactNode;
};

export const SerialProvider = ({ children }: SerialProviderProps) => {
  const [availablePorts, setAvailablePorts] = useState<SerialPort[]>([]);
  const [forcePlates, setForcePlates] = useState<ForcePlate[]>([]);
  const [isScanning, setIsScanning] = useState(false);

  // Buffer for batching data updates
  const dataBuffers = useRef<Record<string, ForceData[]>>({});
  const updateTimers = useRef<Record<string, NodeJS.Timeout>>({});

  // Batch update interval (ms) - update UI every 50ms instead of every data point
  const BATCH_UPDATE_INTERVAL = 50;

  // Check if serial is supported
  const isSerialSupported = 'serial' in navigator;

  // Helper to get a persistent identifier for a port (usbVendorId + usbProductId + serialNumber if available)
  function getPortPersistentId(port: SerialPort): string {
    const info = port.getInfo();
    // Some browsers may not provide serialNumber, fallback to vendor/product
    return [info.usbVendorId, info.usbProductId, info.serialNumber || ''].join('-');
  }

  // Batched update function to reduce React re-renders
  const flushDataBuffer = (plateId: string) => {
    const bufferedData = dataBuffers.current[plateId];
    if (!bufferedData || bufferedData.length === 0) return;

    // Get the latest data point for lastData
    const latestData = bufferedData[bufferedData.length - 1];

    setForcePlates(plates =>
      plates.map(p => {
        if (p.id !== plateId) return p;

        // Limit total data array size to prevent memory issues
        const MAX_DATA_POINTS = 10000;
        const combinedData = [...p.data, ...bufferedData];
        const newData = combinedData.length > MAX_DATA_POINTS
          ? combinedData.slice(-MAX_DATA_POINTS)
          : combinedData;

        return {
          ...p,
          lastData: latestData,
          data: newData
        };
      })
    );

    // Clear the buffer
    dataBuffers.current[plateId] = [];

    // Clear the timer
    if (updateTimers.current[plateId]) {
      clearTimeout(updateTimers.current[plateId]);
      delete updateTimers.current[plateId];
    }
  };

  // Add data to buffer and schedule update
  const addDataToBuffer = (plateId: string, data: ForceData) => {
    // Initialize buffer if needed
    if (!dataBuffers.current[plateId]) {
      dataBuffers.current[plateId] = [];
    }

    // Add data to buffer
    dataBuffers.current[plateId].push(data);

    // Schedule update if not already scheduled
    if (!updateTimers.current[plateId]) {
      updateTimers.current[plateId] = setTimeout(() => {
        flushDataBuffer(plateId);
      }, BATCH_UPDATE_INTERVAL);
    }
  };

  // Scan for available serial ports
  const scanForPorts = async () => {
    if (!isSerialSupported) {
      toast({
        title: "Serial API not supported",
        description: "Your browser doesn't support the Web Serial API. Try using Chrome.",
        variant: "destructive",
      });
      return;
    }

    try {
      setIsScanning(true);
      const ports = await navigator.serial.getPorts();
      setAvailablePorts(ports);
    } catch (error) {
      console.error('Error scanning for ports:', error);
      toast({
        title: "Port Scanning Failed",
        description: "Failed to scan for serial ports. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsScanning(false);
    }
  };

  // Connect to a serial port
  const connectToPort = async (portInfo: SerialPort): Promise<ForcePlate | null> => {
    try {
      // Request port access if needed
      const port = portInfo || await navigator.serial.requestPort();
      await port.open({ baudRate: 115200 });

      const persistentId = getPortPersistentId(port);
      // Try to find an existing plate with this persistentId (even if disconnected)
      let existingPlate = forcePlates.find(p => p.port && getPortPersistentId(p.port) === persistentId);
      if (!existingPlate) {
        // Also check disconnected plates by storing persistentId in id
        existingPlate = forcePlates.find(p => p.id === persistentId);
      }

      if (existingPlate) {
        // Reuse the existing plate (restore port, set connected)
        setForcePlates(prev => prev.map(p =>
          p === existingPlate
            ? { ...p, port, isConnected: true, name: port.getInfo().usbProductName || p.name }
            : p
        ));
        toast({
          title: "Reconnected",
          description: `Reconnected to ${existingPlate.name}`,
        });
        return { ...existingPlate, port, isConnected: true };
      }

      // Otherwise, create a new plate
      const plateId = persistentId;
      const newPlate: ForcePlate = {
        id: plateId,
        port: port,
        reader: null,
        name: port.getInfo().usbProductName || `Force Plate ${forcePlates.length + 1}`,
        isConnected: true,
        isReading: false,
        data: [],
        lastData: null,
      };
      setForcePlates((prev) => [...prev, newPlate]);
      toast({
        title: "Connection Successful",
        description: `Connected to ${newPlate.name}`,
      });
      return newPlate;
    } catch (error) {
      console.error('Error connecting to port:', error);
      toast({
        title: "Connection Failed",
        description: "Failed to connect to the serial port. Please try again.",
        variant: "destructive",
      });
      return null;
    }
  };

  // Disconnect from a serial port
  const disconnectPort = async (plateId: string) => {
    const plate = forcePlates.find(p => p.id === plateId);
    if (!plate) return;

    try {
      if (plate.isReading) {
        await stopReading(plateId);
      }

      if (plate.port && plate.isConnected) {
        await plate.port.close();
      }

      setForcePlates(plates =>
        plates.map(p =>
          p.id === plateId ? { ...p, isConnected: false, port: null } : p
        )
      );

      toast({
        title: "Disconnected",
        description: `Disconnected from ${plate.name}`,
      });
    } catch (error) {
      console.error('Error disconnecting port:', error);
      toast({
        title: "Disconnection Failed",
        description: "Failed to properly disconnect. The port might still be in use.",
        variant: "destructive",
      });
    }
  };

  // Parse binary data from force plate
  const parseForceData = (dataBuffer: Uint8Array): ForceData | null => {
    try {
      // 2 int32 (8 bytes) + 8 ints (32 bytes) + 5 floats (20 bytes) + 2 termination chars = 62 bytes
      if (dataBuffer.length < 62) {
        console.warn('Expected 62 bytes, got:', dataBuffer.length);
        return null;
      }
      const dataView = new DataView(dataBuffer.buffer, dataBuffer.byteOffset, dataBuffer.byteLength);
      // Parse the 2 int32s (timestamp, sampleId)
      const sampleTimestamp = dataView.getInt32(0, true);
      const sampleId = dataView.getInt32(4, true);
      // Parse the 8 ints (raw load cell readings)
      const rawData = [];
      for (let i = 0; i < 8; i++) {
        rawData.push(dataView.getInt32(8 + (i * 4), true));
      }
      // Parse the 5 floats (Fx, Fy, Fz, Cx, Cy)
      const fx = dataView.getFloat32(40, true);
      const fy = dataView.getFloat32(44, true);
      const fz = dataView.getFloat32(48, true);
      const cx = dataView.getFloat32(52, true);
      const cy = dataView.getFloat32(56, true);

      // Check termination chars (\r\n)
      if (dataBuffer[60] !== 13 || dataBuffer[61] !== 10) {
        console.warn('Invalid termination chars in frame');
        // print the raw data for debugging
        console.log('Raw data:', dataBuffer);
        // print the raw data as hex
        console.log('Raw data (hex):', Array.from(dataBuffer).map(b => b.toString(16).padStart(2, '0')).join(' '));
        return null;
      }
      return {
        fx, fy, fz, cx, cy,
        rawData,
        timestamp: Date.now(),
        sampleTimestamp,
        sampleId,
      };
    } catch (error) {
      console.error('Error parsing force data:', error);
      return null;
    }
  };

  // Start reading data from a force plate
  const startReading = async (plateId: string) => {
    const plateIndex = forcePlates.findIndex(p => p.id === plateId);
    if (plateIndex === -1) return;

    const plate = forcePlates[plateIndex];
    if (!plate.port || !plate.isConnected) return;

    // If already reading, stop first to prevent conflicts
    if (plate.isReading) {
      console.warn('Plate is already reading, stopping first...');
      await stopReading(plateId);
      // Wait a bit for cleanup to complete
      await new Promise(resolve => setTimeout(resolve, 100));
    }

    try {
      // Double-check the plate state after potential stop
      const currentPlate = forcePlates.find(p => p.id === plateId);
      if (!currentPlate || !currentPlate.port || !currentPlate.isConnected || currentPlate.isReading) {
        console.warn('Plate state changed during start attempt');
        return;
      }

      const reader = currentPlate.port.readable!.getReader();

      // Send an 's' command to the device to start sending data
      const encoder = new TextEncoder();
      const writer = currentPlate.port.writable?.getWriter();
      if (writer) {
        await writer.write(encoder.encode('s'));
        writer.releaseLock();
      }

      // Update state to indicate reading has started
      setForcePlates(plates =>
        plates.map(p =>
          p.id === plateId ? { ...p, reader, isReading: true } : p
        )
      );

      // Start the reading loop
      readLoop(plateId, reader);

      toast({
        title: "Data Streaming Started",
        description: `Now receiving data from ${currentPlate.name}`,
      });
    } catch (error) {
      console.error('Error starting data read:', error);

      // Make sure state is cleaned up on error
      setForcePlates(plates =>
        plates.map(p =>
          p.id === plateId ? { ...p, isReading: false, reader: null } : p
        )
      );

      toast({
        title: "Read Failed",
        description: "Failed to start reading data from the force plate.",
        variant: "destructive",
      });
    }
  };

  // Loop to continuously read data
  const readLoop = async (plateId: string, reader: ReadableStreamDefaultReader<Uint8Array>) => {
    try {
      // Create a buffer for the incoming data
      let buffer = new Uint8Array(0);
      // Keep reading until stopped
      while (true) {
        const { value, done } = await reader.read();
        if (done) break;
        // Append the new data to our buffer
        const newBuffer = new Uint8Array(buffer.length + value.length);
        newBuffer.set(buffer);
        newBuffer.set(value, buffer.length);
        buffer = newBuffer;
        // Process complete frames (62 bytes each, ending with \r\n)
        while (buffer.length >= 62) {
          // Search for a valid frame ending with \r\n
          let frameStart = -1;
          for (let i = 0; i <= buffer.length - 62; i++) {
            if (buffer[i + 60] === 13 && buffer[i + 61] === 10) {
              frameStart = i;
              break;
            }
          }
          if (frameStart === -1) {
            // No valid frame found, keep the last 61 bytes in case the next chunk completes a frame
            buffer = buffer.slice(-61);
            break;
          }
          const frameData = buffer.slice(frameStart, frameStart + 62);
          buffer = buffer.slice(frameStart + 62);
          const parsedData = parseForceData(frameData);
          if (parsedData) {
            // Add data to buffer instead of immediately updating state
            addDataToBuffer(plateId, parsedData);
          }
        }
      }
    } catch (error) {
      console.error('Error in read loop:', error);

      // Show user-friendly error message
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      if (errorMessage.includes('Break')) {
        toast({
          title: "Data Stream Interrupted",
          description: "The force plate stopped sending data. You can try clicking Start again to resume.",
          variant: "destructive",
        });
      } else {
        toast({
          title: "Read Error",
          description: `Error reading from force plate: ${errorMessage}`,
          variant: "destructive",
        });
      }
    } finally {
      // Always clean up the reader and state
      try {
        reader.releaseLock();
      } catch (e) {
        // Reader might already be released
        console.warn('Reader already released:', e);
      }

      // Flush any remaining buffered data before stopping
      flushDataBuffer(plateId);

      // Clean up timers and buffers
      if (updateTimers.current[plateId]) {
        clearTimeout(updateTimers.current[plateId]);
        delete updateTimers.current[plateId];
      }
      delete dataBuffers.current[plateId];

      // Update state to reflect that reading has stopped
      setForcePlates(plates =>
        plates.map(p =>
          p.id === plateId ? { ...p, isReading: false, reader: null } : p
        )
      );
    }
  };

  // Stop reading data from a force plate
  const stopReading = async (plateId: string) => {
    const plate = forcePlates.find(p => p.id === plateId);
    if (!plate || !plate.isReading || !plate.reader) return;

    try {
      // Send a 'p' command to the device to pause sending data
      const encoder = new TextEncoder();
      const writer = plate.port.writable?.getWriter();
      if (writer) {
        await writer.write(encoder.encode('p'));
        writer.releaseLock();
      }

      await plate.reader.cancel();

      // Clean up buffers and timers
      flushDataBuffer(plateId);
      if (updateTimers.current[plateId]) {
        clearTimeout(updateTimers.current[plateId]);
        delete updateTimers.current[plateId];
      }
      delete dataBuffers.current[plateId];

      setForcePlates(plates =>
        plates.map(p =>
          p.id === plateId ? { ...p, isReading: false, reader: null } : p
        )
      );

      toast({
        title: "Data Streaming Stopped",
        description: `Stopped receiving data from ${plate.name}`,
      });
    } catch (error) {
      console.error('Error stopping data read:', error);
      toast({
        title: "Stop Failed",
        description: "Failed to stop reading data from the force plate.",
        variant: "destructive",
      });
    }
  };

  // Zero/tare a force plate
  const zeroPlate = async (plateId: string) => {
    const plate = forcePlates.find(p => p.id === plateId);
    if (!plate || !plate.port || !plate.isConnected) return;

    try {
      // Send a 'z' command to the device to zero/tare the plate
      const encoder = new TextEncoder();
      const writer = plate.port.writable?.getWriter();
      if (writer) {
        await writer.write(encoder.encode('z'));
        writer.releaseLock();

        toast({
          title: "Plate Zeroed",
          description: `Successfully zeroed ${plate.name}`,
        });
      } else {
        throw new Error('Unable to get writer for serial port');
      }
    } catch (error) {
      console.error('Error zeroing plate:', error);
      toast({
        title: "Zero Failed",
        description: "Failed to zero the force plate. Please try again.",
        variant: "destructive",
      });
    }
  };

  // Helper function to convert ForceData array to CSV format
  const convertToCSV = (data: ForceData[]): string => {
    if (data.length === 0) return '';

    // CSV headers
    const headers = [
      'timestamp',
      'sampleTimestamp',
      'sampleId',
      'fx',
      'fy',
      'fz',
      'cx',
      'cy',
      'rawData_0',
      'rawData_1',
      'rawData_2',
      'rawData_3',
      'rawData_4',
      'rawData_5',
      'rawData_6',
      'rawData_7'
    ];

    // Convert data to CSV rows
    const csvRows = data.map(row => [
      row.timestamp,
      row.sampleTimestamp,
      row.sampleId,
      row.fx,
      row.fy,
      row.fz,
      row.cx,
      row.cy,
      ...row.rawData
    ].join(','));

    // Combine headers and rows
    return [headers.join(','), ...csvRows].join('\n');
  };

  // Save data to file
  const saveData = async (plateId: string, format: DataFormat = 'json', fileName?: string) => {
    const plate = forcePlates.find(p => p.id === plateId);
    if (!plate || plate.data.length === 0) return;

    try {
      let dataContent: string;
      let mimeType: string;
      let fileExtension: string;

      if (format === 'csv') {
        dataContent = convertToCSV(plate.data);
        mimeType = 'text/csv';
        fileExtension = 'csv';
      } else {
        dataContent = JSON.stringify(plate.data, null, 2);
        mimeType = 'application/json';
        fileExtension = 'json';
      }

      // Create blob with the data
      const dataBlob = new Blob([dataContent], { type: mimeType });

      // Create a download link
      const url = URL.createObjectURL(dataBlob);
      const link = document.createElement('a');
      link.href = url;
      link.download = fileName || `${plate.name.replace(/\s+/g, '_')}_${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.${fileExtension}`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);

      toast({
        title: "Data Saved",
        description: `Saved ${plate.data.length} records to ${format.toUpperCase()} file`,
      });
    } catch (error) {
      console.error('Error saving data:', error);
      toast({
        title: "Save Failed",
        description: "Failed to save force plate data to file.",
        variant: "destructive",
      });
    }
  };

  // Clear data for a force plate
  const clearData = (plateId: string) => {
    setForcePlates(plates =>
      plates.map(p =>
        p.id === plateId ? { ...p, data: [] } : p
      )
    );

    toast({
      title: "Data Cleared",
      description: "Force plate data has been cleared.",
    });
  };

  // Scan for ports on initial load
  useEffect(() => {
    if (isSerialSupported) {
      scanForPorts();

      // Set up event listener for when new devices are connected
      navigator.serial.addEventListener('connect', () => {
        scanForPorts();
      });

      // Set up event listener for when devices are disconnected
      navigator.serial.addEventListener('disconnect', () => {
        scanForPorts();
      });

      // Clean up event listeners
      return () => {
        navigator.serial.removeEventListener('connect', scanForPorts);
        navigator.serial.removeEventListener('disconnect', scanForPorts);
      };
    }
  }, []);

  const contextValue: SerialContextType = {
    availablePorts,
    forcePlates,
    isScanning,
    scanForPorts,
    connectToPort,
    disconnectPort,
    startReading,
    stopReading,
    zeroPlate,
    saveData,
    clearData,
  };

  return (
    <SerialContext.Provider value={contextValue}>
      {children}
    </SerialContext.Provider>
  );
};

export const useSerial = (): SerialContextType => {
  const context = useContext(SerialContext);
  if (!context) {
    throw new Error('useSerial must be used within a SerialProvider');
  }
  return context;
};
